import { json, redirect } from '@sveltejs/kit';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { OAuth2Client } from 'google-auth-library';
import { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, PUBLIC_SITE_URL } from '$lib/utils/env.js';

const oauth2Client = new OAuth2Client(
  GOOGLE_CLIENT_ID,
  GOOGLE_CLIENT_SECRET,
  `${PUBLIC_SITE_URL}/api/auth/google/callback`
);

export const GET: RequestHandler = async ({ url }) => {
  try {
    const redirectTo = url.searchParams.get('redirectTo') || '/';

    console.log('Google auth initiated:', {
      redirectTo,
      clientId: GOOGLE_CLIENT_ID,
      siteUrl: PUBLIC_SITE_URL,
      redirectUri: `${PUBLIC_SITE_URL}/api/auth/google/callback`
    });

    // Generate the URL for Google OAuth2 using the proper library
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/userinfo.email'
      ],
      state: redirectTo,
    });

    console.log('Redirecting to Google:', authUrl);

    throw redirect(302, authUrl);
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }

    console.error('Google auth error:', error);
    return json({
      success: false,
      error: 'Failed to initiate Google authentication'
    }, { status: 500 });
  }
};
